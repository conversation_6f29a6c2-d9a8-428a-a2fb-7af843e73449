******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Mon Jul 28 22:57:17 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000079fd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009eb0  00016150  R  X
  SRAM                  20200000   00008000  0000073f  000078c1  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009eb0   00009eb0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00008620   00008620    r-x .text
  000086e0    000086e0    00001740   00001740    r-- .rodata
  00009e20    00009e20    00000090   00000090    r-- .cinit
20200000    20200000    00000540   00000000    rw-
  20200000    20200000    000003a7   00000000    rw- .bss
  202003a8    202003a8    00000198   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00008620     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    000002a0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000138c    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001604    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000183c    0000022c     MPU6050.o (.text.Read_Quad)
                  00001a68    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001c94    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001eb4    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  000020a8    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00002284    000001b0     hwt101_driver.o (.text.HWT101_ProcessBuffer)
                  00002434    000001b0     Task.o (.text.Task_Start)
                  000025e4    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  00002784    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00002916    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002918    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002aa0    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002c18    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002d88    00000168     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002ef0    00000144     MPU6050.o (.text.MPU6050_Init)
                  00003034    0000013c     Tracker.o (.text.Tracker_Read)
                  00003170    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000032ac    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  000033e0    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003514    00000130     OLED.o (.text.OLED_ShowChar)
                  00003644    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00003774    00000128     inv_mpu.o (.text.mpu_init)
                  0000389c    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  000039c0    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003ae4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003c04    00000110     OLED.o (.text.OLED_Init)
                  00003d14    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003e20    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003f28    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  0000402c    00000104     Task_App.o (.text.Task_Motor_PID)
                  00004130    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004230    000000f4     Task_App.o (.text.Task_Init)
                  00004324    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00004410    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000044f4    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  000045d8    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000046b4    000000dc     Task_App.o (.text.Task_OLED)
                  00004790    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004868    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004940    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004a14    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004ae4    000000cc     Task_App.o (.text.Task_IdleFunction)
                  00004bb0    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00004c74    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004d38    000000bc     Motor.o (.text.Motor_Start)
                  00004df4    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004eb0    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004f68    000000b4     Task.o (.text.Task_Add)
                  0000501c    000000b0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000050cc    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00005178    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00005224    000000a8     hwt101_driver.o (.text.HWT101_Create)
                  000052cc    000000a4     Motor.o (.text.Motor_GetSpeed)
                  00005370    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00005412    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00005414    0000009c     Motor.o (.text.Motor_SetDuty)
                  000054b0    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  0000554c    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  000055e4    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  0000567c    00000096     MPU6050.o (.text.inv_row_2_scale)
                  00005712    00000002     --HOLE-- [fill = 0]
                  00005714    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  000057a0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  0000582c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000058b8    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005944    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  000059c8    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005a4c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005ace    00000002     --HOLE-- [fill = 0]
                  00005ad0    00000080     Task_App.o (.text.Task_Serial)
                  00005b50    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005bcc    00000078     Serial.o (.text.my_printf)
                  00005c44    00000074     Interrupt.o (.text.Interrupt_Init)
                  00005cb8    00000074     Motor.o (.text.Motor_SetDirc)
                  00005d2c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005da0    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005e14    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005e88    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005ef8    0000006e     OLED.o (.text.OLED_ShowString)
                  00005f66    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005fd0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00006038    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000609e    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00006104    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00006168    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000061cc    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00006230    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00006292    00000002     --HOLE-- [fill = 0]
                  00006294    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000062f6    00000002     --HOLE-- [fill = 0]
                  000062f8    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00006358    00000060     Key_Led.o (.text.Key_Read)
                  000063b8    00000060     Interrupt.o (.text.UART1_IRQHandler)
                  00006418    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006478    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  000064d8    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00006536    00000002     --HOLE-- [fill = 0]
                  00006538    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006594    0000005c     Task_App.o (.text.Task_Tracker)
                  000065f0    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000664c    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  000066a8    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006704    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  0000675c    00000058     Serial.o (.text.Serial_Init)
                  000067b4    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000680c    00000058            : _printfi.c.obj (.text._pconv_f)
                  00006864    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000068ba    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000690c    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  0000695c    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  000069ac    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000069fc    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006a48    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006a94    0000004c     OLED.o (.text.OLED_Printf)
                  00006ae0    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006b2a    00000002     --HOLE-- [fill = 0]
                  00006b2c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00006b74    00000048     hwt101_driver.o (.text.HWT101_GetGyroZ)
                  00006bbc    00000048     hwt101_driver.o (.text.HWT101_GetYaw)
                  00006c04    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006c4c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00006c94    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006cdc    00000046     hwt101_driver.o (.text.HWT101_CalculateChecksum)
                  00006d22    00000002     --HOLE-- [fill = 0]
                  00006d24    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006d68    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00006dac    00000044     Task_App.o (.text.Task_Key)
                  00006df0    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006e34    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006e78    00000044     OLED.o (.text.mspm0_i2c_disable)
                  00006ebc    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00006efe    00000002     --HOLE-- [fill = 0]
                  00006f00    00000040     hwt101_driver.o (.text.HWT101_ConvertAngleData)
                  00006f40    00000040     hwt101_driver.o (.text.HWT101_ConvertGyroData)
                  00006f80    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00006fc0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00007000    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00007040    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00007080    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  000070c0    0000003e     Task.o (.text.Task_CMP)
                  000070fe    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  0000713c    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00007178    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000071b4    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000071f0    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  0000722c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00007268    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  000072a4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000072e0    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0000731c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00007358    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00007392    00000002     --HOLE-- [fill = 0]
                  00007394    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000073ce    00000038     hwt101_driver.o (.text.HWT101_ParseAnglePacket)
                  00007406    00000038     hwt101_driver.o (.text.HWT101_ParseGyroPacket)
                  0000743e    00000002     --HOLE-- [fill = 0]
                  00007440    00000038     Task_App.o (.text.Task_LED)
                  00007478    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  000074b0    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000074e4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00007518    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0000754c    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00007580    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  000075b2    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  000075e4    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00007614    00000030     hwt101_driver.o (.text.HWT101_ValidateParams)
                  00007644    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007674    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000076a4    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  000076d4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007704    00000030            : vsnprintf.c.obj (.text._outs)
                  00007734    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00007764    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007794    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000077c0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000077ec    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00007818    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00007842    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  0000786a    00000028     OLED.o (.text.DL_Common_updateReg)
                  00007892    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000078ba    00000002     --HOLE-- [fill = 0]
                  000078bc    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  000078e4    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  0000790c    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00007934    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  0000795c    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007984    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  000079ac    00000028     SysTick.o (.text.SysTick_Increasment)
                  000079d4    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000079fc    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00007a24    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007a4a    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  00007a70    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007a96    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007abc    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  00007ae0    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00007b04    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007b26    00000002     --HOLE-- [fill = 0]
                  00007b28    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007b48    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007b68    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  00007b88    00000020     SysTick.o (.text.Delay)
                  00007ba8    00000020     main.o (.text.main)
                  00007bc8    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007be8    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007c06    00000002     --HOLE-- [fill = 0]
                  00007c08    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00007c26    00000002     --HOLE-- [fill = 0]
                  00007c28    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  00007c44    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007c60    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007c7c    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007c98    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007cb4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00007cd0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007cec    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007d08    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00007d24    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  00007d40    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  00007d5c    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007d78    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007d94    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007db0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007dcc    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007de8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007e04    0000001c     Interrupt.o (.text.DL_UART_enableInterrupt)
                  00007e20    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00007e3c    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  00007e58    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007e70    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007e88    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007ea0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007eb8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007ed0    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007ee8    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007f00    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007f18    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00007f30    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  00007f48    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00007f60    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  00007f78    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007f90    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007fa8    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007fc0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007fd8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00007ff0    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00008008    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00008020    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00008038    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00008050    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00008068    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  00008080    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00008098    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000080b0    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000080c8    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  000080e0    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000080f8    00000018     OLED.o (.text.DL_I2C_reset)
                  00008110    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00008128    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00008140    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00008158    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00008170    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00008188    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000081a0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000081b8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000081d0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000081e8    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00008200    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00008218    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00008230    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00008248    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00008260    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00008278    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00008290    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  000082a8    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  000082c0    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000082d8    00000018            : vsprintf.c.obj (.text._outs)
                  000082f0    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00008306    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  0000831c    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00008332    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00008348    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  0000835e    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00008374    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000838a    00000016     SysTick.o (.text.SysGetTick)
                  000083a0    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000083b6    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  000083ca    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  000083de    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000083f2    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00008406    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000841a    00000002     --HOLE-- [fill = 0]
                  0000841c    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00008430    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00008444    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00008458    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  0000846c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00008480    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00008494    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000084a8    00000014     Interrupt.o (.text.DL_UART_receiveData)
                  000084bc    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000084d0    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  000084e4    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  000084f8    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000850c    00000012     Interrupt.o (.text.DL_UART_getPendingInterrupt)
                  0000851e    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00008530    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00008542    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00008554    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00008564    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00008574    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00008584    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00008594    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000085a2    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000085b0    0000000e     MPU6050.o (.text.tap_cb)
                  000085be    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  000085cc    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000085d8    0000000c     SysTick.o (.text.Sys_GetTick)
                  000085e4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000085ee    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000085f8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00008608    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00008612    00000002     --HOLE-- [fill = 0]
                  00008614    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00008624    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000862e    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008638    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008642    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  0000864c    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  0000865c    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00008666    0000000a     MPU6050.o (.text.android_orient_cb)
                  00008670    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00008678    00000008     Interrupt.o (.text.SysTick_Handler)
                  00008680    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00008688    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00008690    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00008696    00000002     --HOLE-- [fill = 0]
                  00008698    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  000086a8    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000086ae    00000006            : exit.c.obj (.text:abort)
                  000086b4    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000086b8    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000086bc    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000086c0    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000086c4    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  000086d4    00000004            : pre_init.c.obj (.text._system_pre_init)
                  000086d8    00000008     --HOLE-- [fill = 0]

.cinit     0    00009e20    00000090     
                  00009e20    00000069     (.cinit..data.load) [load image, compression = lzss]
                  00009e89    00000003     --HOLE-- [fill = 0]
                  00009e8c    0000000c     (__TI_handler_table)
                  00009e98    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009ea0    00000010     (__TI_cinit_table)

.rodata    0    000086e0    00001740     
                  000086e0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  000092d6    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  000098c6    00000228     OLED_Font.o (.rodata.asc2_0806)
                  00009aee    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009af0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009bf1    00000007     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00009bf8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009c38    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00009c60    00000028     inv_mpu.o (.rodata.test)
                  00009c88    00000023     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00009cab    00000022     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00009ccd    00000022     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00009cef    0000001e     inv_mpu.o (.rodata.reg)
                  00009d0d    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  00009d10    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009d28    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009d40    00000014     Task_App.o (.rodata.str1.3850258909703972507.1)
                  00009d54    00000014     Task_App.o (.rodata.str1.4769078833470683459.1)
                  00009d68    00000014     Task_App.o (.rodata.str1.5883415095785080416.1)
                  00009d7c    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00009d8d    00000011     Task_App.o (.rodata.str1.13166305789289702848.1)
                  00009d9e    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00009daf    00000011     Task_App.o (.rodata.str1.7950429023856218820.1)
                  00009dc0    0000000c     inv_mpu.o (.rodata.hw)
                  00009dcc    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  00009dd6    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00009de0    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  00009de8    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  00009df0    00000008     Task_App.o (.rodata.str1.8896853068034818020.1)
                  00009df8    00000006     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00009dfe    00000005     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00009e03    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00009e07    00000004     Task_App.o (.rodata.str1.492715258893803702.1)
                  00009e0b    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  00009e0e    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00009e10    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00009e12    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00009e14    0000000c     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003a7     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    00000044     (.common:hwt101_instance)
                  20200334    00000040     (.common:HWT101_RxBuffer)
                  20200374    00000010     (.common:quat)
                  20200384    00000006     (.common:Data_Accel)
                  2020038a    00000006     (.common:Data_Gyro)
                  20200390    00000004     (.common:Data_Pitch)
                  20200394    00000004     (.common:Data_Roll)
                  20200398    00000004     (.common:Data_Yaw)
                  2020039c    00000004     (.common:ExISR_Flag)
                  202003a0    00000004     (.common:sensor_timestamp)
                  202003a4    00000002     (.common:sensors)
                  202003a6    00000001     (.common:more)

.data      0    202003a8    00000198     UNINITIALIZED
                  202003a8    00000040     Motor.o (.data.Motor_Back_Left)
                  202003e8    00000040     Motor.o (.data.Motor_Back_Right)
                  20200428    00000040     Motor.o (.data.Motor_Font_Left)
                  20200468    00000040     Motor.o (.data.Motor_Font_Right)
                  202004a8    0000002c     inv_mpu.o (.data.st)
                  202004d4    00000010     Task_App.o (.data.Motor)
                  202004e4    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004f4    0000000e     MPU6050.o (.data.hal)
                  20200502    00000009     MPU6050.o (.data.gyro_orientation)
                  2020050b    00000001     Interrupt.o (.data.Flag_HWT101_DataReady)
                  2020050c    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200514    00000008     Task_App.o (.data.Data_Tracker_Input)
                  2020051c    00000008     Serial.o (.data.huart1)
                  20200524    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  20200528    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  2020052c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20200530    00000004     SysTick.o (.data.delayTick)
                  20200534    00000004     SysTick.o (.data.uwTick)
                  20200538    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  2020053a    00000001     Task_App.o (.data.Flag_LED)
                  2020053b    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  2020053c    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  2020053d    00000001     Task.o (.data.Task_Num)
                  2020053e    00000001     Interrupt.o (.data.UART1_IRQHandler.rxIndex)
                  2020053f    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3546    138       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3586    330       0      
                                                               
    .\APP\Src\
       Task_App.o                     1316    231       112    
       Interrupt.o                    816     0         72     
    +--+------------------------------+-------+---------+---------+
       Total:                         2132    231       184    
                                                               
    .\BSP\Src\
       MPU6050.o                      2472    0         70     
       OLED_Font.o                    0       2072      0      
       OLED.o                         1854    0         0      
       hwt101_driver.o                1102    0         0      
       Serial.o                       524     0         520    
       Motor.o                        692     0         256    
       Task.o                         674     0         241    
       PID_IQMath.o                   402     0         0      
       Tracker.o                      338     0         0      
       Key_Led.o                      118     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         8282    2072      1095   
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      122     0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1144    0         0      
                                                               
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNdiv.o                      24      0         0      
       _IQNmpy.o                      24      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         0      
                                                               
    D:\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8356    355       4      
                                                               
    D:\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2984    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       141       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   34294   6273      1855   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00009ea0 records: 2, size/record: 8, table size: 16
	.data: load addr=00009e20, load size=00000069 bytes, run addr=202003a8, run size=00000198 bytes, compression=lzss
	.bss: load addr=00009e98, load size=00000008 bytes, run addr=20200000, run size=000003a7 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00009e8c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002785     000085f8     000085f6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004411     00008614     00008610   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000862c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00008640          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00008676          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             000086ac          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003d15     0000864c     0000864a   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   0000278f     00008698     00008694   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             000086be          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000079fd     000086c4     000086c0   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000086b5  ADC0_IRQHandler                      
000086b5  ADC1_IRQHandler                      
000086b5  AES_IRQHandler                       
000086b8  C$$EXIT                              
000086b5  CANFD0_IRQHandler                    
000086b5  DAC0_IRQHandler                      
000085e5  DL_Common_delayCycles                
000069fd  DL_DMA_initChannel                   
000064d9  DL_I2C_fillControllerTXFIFO          
000071f1  DL_I2C_flushControllerTXFIFO         
00007a97  DL_I2C_setClockConfig                
000045d9  DL_SYSCTL_configSYSPLL               
00006105  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006d25  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003f29  DL_Timer_initFourCCPWMMode           
00007dcd  DL_Timer_setCaptCompUpdateMethod     
000081b9  DL_Timer_setCaptureCompareOutCtl     
00008565  DL_Timer_setCaptureCompareValue      
00007de9  DL_Timer_setClockConfig              
00006b2d  DL_UART_init                         
0000851f  DL_UART_setClockConfig               
00007b69  DL_UART_transmitDataBlocking         
000086b5  DMA_IRQHandler                       
20200384  Data_Accel                           
2020038a  Data_Gyro                            
2020050c  Data_MotorEncoder                    
20200524  Data_Motor_TarSpeed                  
20200390  Data_Pitch                           
20200394  Data_Roll                            
20200514  Data_Tracker_Input                   
20200528  Data_Tracker_Offset                  
20200398  Data_Yaw                             
000086b5  Default_Handler                      
00007b89  Delay                                
2020039c  ExISR_Flag                           
2020050b  Flag_HWT101_DataReady                
2020053a  Flag_LED                             
2020053b  Flag_MPU6050_Ready                   
000086b5  GROUP0_IRQHandler                    
00002d89  GROUP1_IRQHandler                    
000086b9  HOSTexit                             
00005225  HWT101_Create                        
00006b75  HWT101_GetGyroZ                      
00006bbd  HWT101_GetYaw                        
00002285  HWT101_ProcessBuffer                 
20200334  HWT101_RxBuffer                      
000086b5  HardFault_Handler                    
000086b5  I2C0_IRQHandler                      
000086b5  I2C1_IRQHandler                      
00005f67  I2C_OLED_Clear                       
00007269  I2C_OLED_Set_Pos                     
0000554d  I2C_OLED_WR_Byte                     
000062f9  I2C_OLED_i2c_sda_unlock              
00005c45  Interrupt_Init                       
00006359  Key_Read                             
00002ef1  MPU6050_Init                         
202004d4  Motor                                
202003a8  Motor_Back_Left                      
202003e8  Motor_Back_Right                     
20200428  Motor_Font_Left                      
20200468  Motor_Font_Right                     
000052cd  Motor_GetSpeed                       
00005415  Motor_SetDuty                        
00004d39  Motor_Start                          
00005e89  MyPrintf_DMA                         
000086b5  NMI_Handler                          
00003c05  OLED_Init                            
00006a95  OLED_Printf                          
00003515  OLED_ShowChar                        
00005ef9  OLED_ShowString                      
00007819  PID_IQ_Init                          
0000389d  PID_IQ_Prosc                         
00006d69  PID_IQ_SetParams                     
000086b5  PendSV_Handler                       
000086b5  RTC_IRQHandler                       
0000183d  Read_Quad                            
000086c1  Reset_Handler                        
000086b5  SPI0_IRQHandler                      
000086b5  SPI1_IRQHandler                      
000086b5  SVC_Handler                          
00007645  SYSCFG_DL_DMA_CH_RX_init             
00008279  SYSCFG_DL_DMA_CH_TX_init             
000085cd  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
00006705  SYSCFG_DL_I2C_MPU6050_init           
00006169  SYSCFG_DL_I2C_OLED_init              
00005715  SYSCFG_DL_MotorBack_init             
000057a1  SYSCFG_DL_MotorFront_init            
00006539  SYSCFG_DL_SYSCTL_init                
00008575  SYSCFG_DL_SYSTICK_init               
00005945  SYSCFG_DL_UART0_init                 
00006f81  SYSCFG_DL_UART_1_init                
00007675  SYSCFG_DL_init                       
0000501d  SYSCFG_DL_initPower                  
0000675d  Serial_Init                          
20200000  Serial_RxData                        
0000838b  SysGetTick                           
00008679  SysTick_Handler                      
000079ad  SysTick_Increasment                  
000085d9  Sys_GetTick                          
000086b5  TIMA0_IRQHandler                     
000086b5  TIMA1_IRQHandler                     
000086b5  TIMG0_IRQHandler                     
000086b5  TIMG12_IRQHandler                    
000086b5  TIMG6_IRQHandler                     
000086b5  TIMG7_IRQHandler                     
000086b5  TIMG8_IRQHandler                     
00008531  TI_memcpy_small                      
000085bf  TI_memset_small                      
00004f69  Task_Add                             
00004ae5  Task_IdleFunction                    
00004231  Task_Init                            
00006dad  Task_Key                             
00007441  Task_LED                             
0000402d  Task_Motor_PID                       
000046b5  Task_OLED                            
00005ad1  Task_Serial                          
00002435  Task_Start                           
00006595  Task_Tracker                         
00003035  Tracker_Read                         
000086b5  UART0_IRQHandler                     
000063b9  UART1_IRQHandler                     
000086b5  UART2_IRQHandler                     
000086b5  UART3_IRQHandler                     
00008291  _IQ24div                             
000082a9  _IQ24mpy                             
000076a5  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00009ea0  __TI_CINIT_Base                      
00009eb0  __TI_CINIT_Limit                     
00009eb0  __TI_CINIT_Warm                      
00009e8c  __TI_Handler_Table_Base              
00009e98  __TI_Handler_Table_Limit             
0000731d  __TI_auto_init_nobinit_nopinit       
00005b51  __TI_decompress_lzss                 
00008543  __TI_decompress_none                 
000067b5  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000083a1  __TI_zero_init_nomemset              
0000278f  __adddf3                             
00004873  __addsf3                             
00009af0  __aeabi_ctype_table_                 
00009af0  __aeabi_ctype_table_C                
00005da1  __aeabi_d2f                          
00006ae1  __aeabi_d2iz                         
00006ebd  __aeabi_d2uiz                        
0000278f  __aeabi_dadd                         
00006231  __aeabi_dcmpeq                       
0000626d  __aeabi_dcmpge                       
00006281  __aeabi_dcmpgt                       
00006259  __aeabi_dcmple                       
00006245  __aeabi_dcmplt                       
00003d15  __aeabi_ddiv                         
00004411  __aeabi_dmul                         
00002785  __aeabi_dsub                         
2020052c  __aeabi_errno                        
00008681  __aeabi_errno_addr                   
00007001  __aeabi_f2d                          
00007479  __aeabi_f2iz                         
00004873  __aeabi_fadd                         
00006295  __aeabi_fcmpeq                       
000062d1  __aeabi_fcmpge                       
000062e5  __aeabi_fcmpgt                       
000062bd  __aeabi_fcmple                       
000062a9  __aeabi_fcmplt                       
00005a4d  __aeabi_fdiv                         
0000582d  __aeabi_fmul                         
00004869  __aeabi_fsub                         
000077c1  __aeabi_i2d                          
000072a5  __aeabi_i2f                          
00006865  __aeabi_idiv                         
00002917  __aeabi_idiv0                        
00006865  __aeabi_idivmod                      
00005413  __aeabi_ldiv0                        
00007c09  __aeabi_llsl                         
00007ae1  __aeabi_lmul                         
00008689  __aeabi_memcpy                       
00008689  __aeabi_memcpy4                      
00008689  __aeabi_memcpy8                      
00008595  __aeabi_memset                       
00008595  __aeabi_memset4                      
00008595  __aeabi_memset8                      
000079d5  __aeabi_ui2f                         
00006fc1  __aeabi_uidiv                        
00006fc1  __aeabi_uidivmod                     
000084bd  __aeabi_uldivmod                     
00007c09  __ashldi3                            
ffffffff  __binit__                            
00005fd1  __cmpdf2                             
00007359  __cmpsf2                             
00003d15  __divdf3                             
00005a4d  __divsf3                             
00005fd1  __eqdf2                              
00007359  __eqsf2                              
00007001  __extendsfdf2                        
00006ae1  __fixdfsi                            
00007479  __fixsfsi                            
00006ebd  __fixunsdfsi                         
000077c1  __floatsidf                          
000072a5  __floatsisf                          
000079d5  __floatunsisf                        
00005d2d  __gedf2                              
000072e1  __gesf2                              
00005d2d  __gtdf2                              
000072e1  __gtsf2                              
00005fd1  __ledf2                              
00007359  __lesf2                              
00005fd1  __ltdf2                              
00007359  __ltsf2                              
UNDEFED   __mpu_init                           
00004411  __muldf3                             
00007ae1  __muldi3                             
00007395  __muldsi3                            
0000582d  __mulsf3                             
00005fd1  __nedf2                              
00007359  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00002785  __subdf3                             
00004869  __subsf3                             
00005da1  __truncdfsf2                         
00005371  __udivmoddi4                         
000079fd  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000086d5  _system_pre_init                     
000086af  abort                                
000098c6  asc2_0806                            
000092d6  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002919  atan2                                
00002919  atan2l                               
00000df5  atanl                                
00007041  atoi                                 
ffffffff  binit                                
20200530  delayTick                            
00006c05  dmp_enable_6x_lp_quat                
0000138d  dmp_enable_feature                   
00006419  dmp_enable_gyro_cal                  
00006c4d  dmp_enable_lp_quat                   
00007e3d  dmp_load_motion_driver_firmware      
00001eb5  dmp_read_fifo                        
000084d1  dmp_register_android_orient_cb       
000084e5  dmp_register_tap_cb                  
000055e5  dmp_set_fifo_rate                    
00002aa1  dmp_set_orientation                  
00006df1  dmp_set_shake_reject_thresh          
00007581  dmp_set_shake_reject_time            
000075b3  dmp_set_shake_reject_timeout         
0000609f  dmp_set_tap_axes                     
00006e35  dmp_set_tap_count                    
00001605  dmp_set_tap_thresh                   
00007735  dmp_set_tap_time                     
00007765  dmp_set_tap_time_multi               
2020053f  enable_group1_irq                    
000065f1  frexp                                
000065f1  frexpl                               
2020051c  huart1                               
00009dc0  hw                                   
202002f0  hwt101_instance                      
00000000  interruptVectors                     
00004791  ldexp                                
00004791  ldexpl                               
00007ba9  main                                 
00007b05  memccpy                              
00007bc9  memcmp                               
202003a6  more                                 
000061cd  mpu6050_i2c_sda_unlock               
00004df5  mpu_configure_fifo                   
00005e15  mpu_get_accel_fsr                    
00006479  mpu_get_gyro_fsr                     
0000754d  mpu_get_sample_rate                  
00003775  mpu_init                             
000039c1  mpu_load_firmware                    
00004131  mpu_lp_accel_mode                    
00003e21  mpu_read_fifo_stream                 
000050cd  mpu_read_mem                         
00001a69  mpu_reset_fifo                       
000044f5  mpu_set_accel_fsr                    
000025e5  mpu_set_bypass                       
00004eb1  mpu_set_dmp_state                    
00004bb1  mpu_set_gyro_fsr                     
000054b1  mpu_set_int_latched                  
00004a15  mpu_set_lpf                          
00004325  mpu_set_sample_rate                  
00003645  mpu_set_sensors                      
00005179  mpu_write_mem                        
000032ad  mspm0_i2c_read                       
00004c75  mspm0_i2c_write                      
00005bcd  my_printf                            
000033e1  qsort                                
20200374  quat                                 
00009cef  reg                                  
00004791  scalbn                               
00004791  scalbnl                              
202003a0  sensor_timestamp                     
202003a4  sensors                              
00002c19  sqrt                                 
00002c19  sqrtl                                
00009c60  test                                 
20200534  uwTick                               
00007081  vsnprintf                            
000077ed  vsprintf                             
00008585  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
0000138d  dmp_enable_feature                   
00001605  dmp_set_tap_thresh                   
0000183d  Read_Quad                            
00001a69  mpu_reset_fifo                       
00001eb5  dmp_read_fifo                        
00002285  HWT101_ProcessBuffer                 
00002435  Task_Start                           
000025e5  mpu_set_bypass                       
00002785  __aeabi_dsub                         
00002785  __subdf3                             
0000278f  __adddf3                             
0000278f  __aeabi_dadd                         
00002917  __aeabi_idiv0                        
00002919  atan2                                
00002919  atan2l                               
00002aa1  dmp_set_orientation                  
00002c19  sqrt                                 
00002c19  sqrtl                                
00002d89  GROUP1_IRQHandler                    
00002ef1  MPU6050_Init                         
00003035  Tracker_Read                         
000032ad  mspm0_i2c_read                       
000033e1  qsort                                
00003515  OLED_ShowChar                        
00003645  mpu_set_sensors                      
00003775  mpu_init                             
0000389d  PID_IQ_Prosc                         
000039c1  mpu_load_firmware                    
00003c05  OLED_Init                            
00003d15  __aeabi_ddiv                         
00003d15  __divdf3                             
00003e21  mpu_read_fifo_stream                 
00003f29  DL_Timer_initFourCCPWMMode           
0000402d  Task_Motor_PID                       
00004131  mpu_lp_accel_mode                    
00004231  Task_Init                            
00004325  mpu_set_sample_rate                  
00004411  __aeabi_dmul                         
00004411  __muldf3                             
000044f5  mpu_set_accel_fsr                    
000045d9  DL_SYSCTL_configSYSPLL               
000046b5  Task_OLED                            
00004791  ldexp                                
00004791  ldexpl                               
00004791  scalbn                               
00004791  scalbnl                              
00004869  __aeabi_fsub                         
00004869  __subsf3                             
00004873  __addsf3                             
00004873  __aeabi_fadd                         
00004a15  mpu_set_lpf                          
00004ae5  Task_IdleFunction                    
00004bb1  mpu_set_gyro_fsr                     
00004c75  mspm0_i2c_write                      
00004d39  Motor_Start                          
00004df5  mpu_configure_fifo                   
00004eb1  mpu_set_dmp_state                    
00004f69  Task_Add                             
0000501d  SYSCFG_DL_initPower                  
000050cd  mpu_read_mem                         
00005179  mpu_write_mem                        
00005225  HWT101_Create                        
000052cd  Motor_GetSpeed                       
00005371  __udivmoddi4                         
00005413  __aeabi_ldiv0                        
00005415  Motor_SetDuty                        
000054b1  mpu_set_int_latched                  
0000554d  I2C_OLED_WR_Byte                     
000055e5  dmp_set_fifo_rate                    
00005715  SYSCFG_DL_MotorBack_init             
000057a1  SYSCFG_DL_MotorFront_init            
0000582d  __aeabi_fmul                         
0000582d  __mulsf3                             
00005945  SYSCFG_DL_UART0_init                 
00005a4d  __aeabi_fdiv                         
00005a4d  __divsf3                             
00005ad1  Task_Serial                          
00005b51  __TI_decompress_lzss                 
00005bcd  my_printf                            
00005c45  Interrupt_Init                       
00005d2d  __gedf2                              
00005d2d  __gtdf2                              
00005da1  __aeabi_d2f                          
00005da1  __truncdfsf2                         
00005e15  mpu_get_accel_fsr                    
00005e89  MyPrintf_DMA                         
00005ef9  OLED_ShowString                      
00005f67  I2C_OLED_Clear                       
00005fd1  __cmpdf2                             
00005fd1  __eqdf2                              
00005fd1  __ledf2                              
00005fd1  __ltdf2                              
00005fd1  __nedf2                              
0000609f  dmp_set_tap_axes                     
00006105  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006169  SYSCFG_DL_I2C_OLED_init              
000061cd  mpu6050_i2c_sda_unlock               
00006231  __aeabi_dcmpeq                       
00006245  __aeabi_dcmplt                       
00006259  __aeabi_dcmple                       
0000626d  __aeabi_dcmpge                       
00006281  __aeabi_dcmpgt                       
00006295  __aeabi_fcmpeq                       
000062a9  __aeabi_fcmplt                       
000062bd  __aeabi_fcmple                       
000062d1  __aeabi_fcmpge                       
000062e5  __aeabi_fcmpgt                       
000062f9  I2C_OLED_i2c_sda_unlock              
00006359  Key_Read                             
000063b9  UART1_IRQHandler                     
00006419  dmp_enable_gyro_cal                  
00006479  mpu_get_gyro_fsr                     
000064d9  DL_I2C_fillControllerTXFIFO          
00006539  SYSCFG_DL_SYSCTL_init                
00006595  Task_Tracker                         
000065f1  frexp                                
000065f1  frexpl                               
00006705  SYSCFG_DL_I2C_MPU6050_init           
0000675d  Serial_Init                          
000067b5  __TI_ltoa                            
00006865  __aeabi_idiv                         
00006865  __aeabi_idivmod                      
000069fd  DL_DMA_initChannel                   
00006a95  OLED_Printf                          
00006ae1  __aeabi_d2iz                         
00006ae1  __fixdfsi                            
00006b2d  DL_UART_init                         
00006b75  HWT101_GetGyroZ                      
00006bbd  HWT101_GetYaw                        
00006c05  dmp_enable_6x_lp_quat                
00006c4d  dmp_enable_lp_quat                   
00006d25  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006d69  PID_IQ_SetParams                     
00006dad  Task_Key                             
00006df1  dmp_set_shake_reject_thresh          
00006e35  dmp_set_tap_count                    
00006ebd  __aeabi_d2uiz                        
00006ebd  __fixunsdfsi                         
00006f81  SYSCFG_DL_UART_1_init                
00006fc1  __aeabi_uidiv                        
00006fc1  __aeabi_uidivmod                     
00007001  __aeabi_f2d                          
00007001  __extendsfdf2                        
00007041  atoi                                 
00007081  vsnprintf                            
000071f1  DL_I2C_flushControllerTXFIFO         
00007269  I2C_OLED_Set_Pos                     
000072a5  __aeabi_i2f                          
000072a5  __floatsisf                          
000072e1  __gesf2                              
000072e1  __gtsf2                              
0000731d  __TI_auto_init_nobinit_nopinit       
00007359  __cmpsf2                             
00007359  __eqsf2                              
00007359  __lesf2                              
00007359  __ltsf2                              
00007359  __nesf2                              
00007395  __muldsi3                            
00007441  Task_LED                             
00007479  __aeabi_f2iz                         
00007479  __fixsfsi                            
0000754d  mpu_get_sample_rate                  
00007581  dmp_set_shake_reject_time            
000075b3  dmp_set_shake_reject_timeout         
00007645  SYSCFG_DL_DMA_CH_RX_init             
00007675  SYSCFG_DL_init                       
000076a5  _IQ24toF                             
00007735  dmp_set_tap_time                     
00007765  dmp_set_tap_time_multi               
000077c1  __aeabi_i2d                          
000077c1  __floatsidf                          
000077ed  vsprintf                             
00007819  PID_IQ_Init                          
000079ad  SysTick_Increasment                  
000079d5  __aeabi_ui2f                         
000079d5  __floatunsisf                        
000079fd  _c_int00_noargs                      
00007a97  DL_I2C_setClockConfig                
00007ae1  __aeabi_lmul                         
00007ae1  __muldi3                             
00007b05  memccpy                              
00007b69  DL_UART_transmitDataBlocking         
00007b89  Delay                                
00007ba9  main                                 
00007bc9  memcmp                               
00007c09  __aeabi_llsl                         
00007c09  __ashldi3                            
00007dcd  DL_Timer_setCaptCompUpdateMethod     
00007de9  DL_Timer_setClockConfig              
00007e3d  dmp_load_motion_driver_firmware      
000081b9  DL_Timer_setCaptureCompareOutCtl     
00008279  SYSCFG_DL_DMA_CH_TX_init             
00008291  _IQ24div                             
000082a9  _IQ24mpy                             
0000838b  SysGetTick                           
000083a1  __TI_zero_init_nomemset              
000084bd  __aeabi_uldivmod                     
000084d1  dmp_register_android_orient_cb       
000084e5  dmp_register_tap_cb                  
0000851f  DL_UART_setClockConfig               
00008531  TI_memcpy_small                      
00008543  __TI_decompress_none                 
00008565  DL_Timer_setCaptureCompareValue      
00008575  SYSCFG_DL_SYSTICK_init               
00008585  wcslen                               
00008595  __aeabi_memset                       
00008595  __aeabi_memset4                      
00008595  __aeabi_memset8                      
000085bf  TI_memset_small                      
000085cd  SYSCFG_DL_DMA_init                   
000085d9  Sys_GetTick                          
000085e5  DL_Common_delayCycles                
00008679  SysTick_Handler                      
00008681  __aeabi_errno_addr                   
00008689  __aeabi_memcpy                       
00008689  __aeabi_memcpy4                      
00008689  __aeabi_memcpy8                      
000086af  abort                                
000086b5  ADC0_IRQHandler                      
000086b5  ADC1_IRQHandler                      
000086b5  AES_IRQHandler                       
000086b5  CANFD0_IRQHandler                    
000086b5  DAC0_IRQHandler                      
000086b5  DMA_IRQHandler                       
000086b5  Default_Handler                      
000086b5  GROUP0_IRQHandler                    
000086b5  HardFault_Handler                    
000086b5  I2C0_IRQHandler                      
000086b5  I2C1_IRQHandler                      
000086b5  NMI_Handler                          
000086b5  PendSV_Handler                       
000086b5  RTC_IRQHandler                       
000086b5  SPI0_IRQHandler                      
000086b5  SPI1_IRQHandler                      
000086b5  SVC_Handler                          
000086b5  TIMA0_IRQHandler                     
000086b5  TIMA1_IRQHandler                     
000086b5  TIMG0_IRQHandler                     
000086b5  TIMG12_IRQHandler                    
000086b5  TIMG6_IRQHandler                     
000086b5  TIMG7_IRQHandler                     
000086b5  TIMG8_IRQHandler                     
000086b5  UART0_IRQHandler                     
000086b5  UART2_IRQHandler                     
000086b5  UART3_IRQHandler                     
000086b8  C$$EXIT                              
000086b9  HOSTexit                             
000086c1  Reset_Handler                        
000086d5  _system_pre_init                     
000092d6  asc2_1608                            
000098c6  asc2_0806                            
00009af0  __aeabi_ctype_table_                 
00009af0  __aeabi_ctype_table_C                
00009c60  test                                 
00009cef  reg                                  
00009dc0  hw                                   
00009e8c  __TI_Handler_Table_Base              
00009e98  __TI_Handler_Table_Limit             
00009ea0  __TI_CINIT_Base                      
00009eb0  __TI_CINIT_Limit                     
00009eb0  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  hwt101_instance                      
20200334  HWT101_RxBuffer                      
20200374  quat                                 
20200384  Data_Accel                           
2020038a  Data_Gyro                            
20200390  Data_Pitch                           
20200394  Data_Roll                            
20200398  Data_Yaw                             
2020039c  ExISR_Flag                           
202003a0  sensor_timestamp                     
202003a4  sensors                              
202003a6  more                                 
202003a8  Motor_Back_Left                      
202003e8  Motor_Back_Right                     
20200428  Motor_Font_Left                      
20200468  Motor_Font_Right                     
202004d4  Motor                                
2020050b  Flag_HWT101_DataReady                
2020050c  Data_MotorEncoder                    
20200514  Data_Tracker_Input                   
2020051c  huart1                               
20200524  Data_Motor_TarSpeed                  
20200528  Data_Tracker_Offset                  
2020052c  __aeabi_errno                        
20200530  delayTick                            
20200534  uwTick                               
2020053a  Flag_LED                             
2020053b  Flag_MPU6050_Ready                   
2020053f  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[322 symbols]
