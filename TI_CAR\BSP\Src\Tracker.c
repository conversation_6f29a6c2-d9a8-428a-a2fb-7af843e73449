#include "Tracker.h"

// 传感器间距定义：相邻两个传感器之间的物理距离
#define DIS_INRERVAL _IQ(1.5)  // 1.5厘米间距（IQ格式定点数）

/**
 * @brief 循迹传感器读取与位置计算函数 - 相当于人眼"看路"的功能
 *
 * 功能说明：
 * 1. 读取8个红外传感器的状态（检测黑线）
 * 2. 使用加权平均算法计算小车相对于黑线的位置偏差
 * 3. 为循迹控制提供精确的位置反馈信息
 *
 * @param tck_ptr 8路传感器数据数组指针，用于存储传感器读取结果
 * @param offset_ptr 位置偏差值指针 (单位:cm)
 *                   负值：小车偏向左侧，需要向右调整
 *                   正值：小车偏向右侧，需要向左调整
 *                   零值：小车在黑线中央，无需调整
 * @return true=成功读取并计算偏差，false=读取失败或参数错误
 */
bool Tracker_Read(uint8_t *tck_ptr, _iq *offset_ptr)
{
    // 安全检查：确保传入的指针不为空
    if (tck_ptr == NULL || offset_ptr == NULL) return false;

    // ========== 第一步：读取8个循迹传感器的状态 ==========
    // 传感器布局：[0][1][2][3][4][5][6][7]
    // 位置关系：  左侧 ←←←← 中心 →→→→ 右侧
    // 每个传感器返回：1=检测到黑线，0=未检测到黑线

    tck_ptr[0] = DL_GPIO_readPins(Tracker_PORT, Tracker__1_PIN);  // 最左侧传感器（位置-5.25cm）
    tck_ptr[1] = DL_GPIO_readPins(Tracker_PORT, Tracker__2_PIN);  // 左侧传感器（位置-3.75cm）
    tck_ptr[2] = DL_GPIO_readPins(Tracker_PORT, Tracker__3_PIN);  // 左中传感器（位置-2.25cm）
    tck_ptr[3] = DL_GPIO_readPins(Tracker_PORT, Tracker__4_PIN);  // 左中心传感器（位置-0.75cm）
    tck_ptr[4] = DL_GPIO_readPins(Tracker_PORT, Tracker__5_PIN);  // 右中心传感器（位置+0.75cm）
    tck_ptr[5] = DL_GPIO_readPins(Tracker_PORT, Tracker__6_PIN);  // 右中传感器（位置+2.25cm）
    tck_ptr[6] = DL_GPIO_readPins(Tracker_PORT, Tracker__7_PIN);  // 右侧传感器（位置+3.75cm）
    tck_ptr[7] = DL_GPIO_readPins(Tracker_PORT, Tracker__8_PIN);  // 最右侧传感器（位置+5.25cm）

    // ========== 第二步：使用加权平均算法计算位置偏差 ==========
    // 就像人眼看路时，大脑会综合分析看到的信息判断偏离程度

    _iq pos_sum = _IQ(0);  // 位置加权和，用于累计所有检测到黑线的传感器位置
    uint8_t cnt = 0;       // 检测到黑线的传感器数量计数器

    // 遍历所有8个传感器，计算加权平均位置
    for (uint8_t i = 0; i < 8; i++)
    {
        if (tck_ptr[i] == TRACK_ON)  // 如果第i个传感器检测到黑线
        {
            // ========== 计算传感器的物理位置 ==========
            // 传感器编号到物理位置的映射：
            // 编号: 0    1    2    3    4    5    6    7
            // 位置:-5.25 -3.75 -2.25 -0.75 +0.75 +2.25 +3.75 +5.25 (cm)
            // 公式: 位置 = (编号 - 3.5) × 1.5cm

            _iq sensor_pos = _IQmpy(_IQ(i - 3.5f), DIS_INRERVAL);  // 计算传感器物理位置
            pos_sum += sensor_pos;  // 累加到位置总和中
            cnt++;                  // 检测到黑线的传感器数量+1
        }
    }

    // ========== 第三步：处理异常情况 ==========
    if (cnt == 0)
    {
        // 所有传感器都没检测到黑线，可能的原因：
        // 1. 小车完全偏离轨道
        // 2. 传感器故障
        // 3. 环境光线干扰
        // 处理策略：保持上次的偏差值，不更新位置信息
        return false;
    }

    // ========== 第四步：计算最终的位置偏差 ==========
    // 加权平均位置 = 所有检测到黑线的传感器位置之和 ÷ 传感器数量
    // 这样可以得到黑线的"重心"位置，即小车应该对准的目标位置
    *offset_ptr = _IQdiv(pos_sum, _IQ(cnt));

    return true;  // 返回计算成功
}