#ifndef __Serial_h
#define __Serial_h

#include "SysConfig.h"

#define MAX_RX_LENGTH 512
#define MAX_TX_LENGTH 512

extern uint8_t Serial_RxData[MAX_RX_LENGTH]; //数据接收数组
extern bool Flag_Serial_RXcplt; //接收完成标志位
extern uint16_t Serial_RxLength; //接收数据长度

void Serial_Init(void);
uint16_t MyPrintf(char *format, ...);
uint16_t MyPrintf_DMA(char *format, ...);
void Serial_ProcessRxData(void); //处理接收数据

#endif
