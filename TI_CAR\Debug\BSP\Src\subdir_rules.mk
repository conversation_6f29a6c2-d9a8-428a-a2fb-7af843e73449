################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
BSP/Src/%.o: ../BSP/Src/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"C:/ccs/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"D:/本届电赛资料库/TI_CAr/TI_CAR/TI_CAR/BSP/Inc" -I"D:/本届电赛资料库/TI_CAr/TI_CAR/TI_CAR" -I"D:/本届电赛资料库/TI_CAr/TI_CAR/TI_CAR/DMP" -I"D:/本届电赛资料库/TI_CAr/TI_CAR/TI_CAR/App/Inc" -I"D:/本届电赛资料库/TI_CAr/TI_CAR/TI_CAR/Debug" -I"C:/ccs/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"C:/ccs/mspm0_sdk_2_05_01_00/source" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"BSP/Src/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


