#ifndef __SysConfig_h
#define __SysConfig_h

/*MACRO*/
#define DEBUG            //调试模式必须保留 非调试可以注释
#define MOTOR_FULL_VALUE 260 //电机转一圈的编码值

/*STD C*/
#include <stdint.h>
#include <stdarg.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdio.h>
#include <math.h>

/*TI CCS*/
#include "ti_msp_dl_config.h"
#include "ti/iqmath/include/IQmathLib.h"

/*HAL库适配层定义(用于hwt101_driver兼容)*/
typedef struct {
    void* Instance;  // UART实例，映射到UART_1_INST
    uint32_t Init;   // 初始化参数（预留）
} UART_HandleTypeDef;

/* 函数映射宏定义 */
#define HAL_Delay(ms)           Delay(ms)
#define HAL_GetTick()           Sys_GetTick()

/* 全局变量声明 */
extern UART_HandleTypeDef huart1;

/* 串口发送函数声明 */
uint16_t my_printf(UART_HandleTypeDef* huart, const char* format, ...);

/*BSP*/
#include "PID_IQMath.h"
#include "Task.h"
#include "Key_Led.h"
#include "SysTick.h"
#include "Motor.h"
#include "Serial.h"
#include "Tracker.h"
#include "OLED.h"
#include "MPU6050.h"
#include "hwt101_driver.h"
//#include "PID.h"

/*APP*/
#include "Task_App.h"
#include "Interrupt.h"

#endif
