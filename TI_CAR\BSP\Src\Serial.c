#include "Serial.h"

uint8_t Serial_RxData[MAX_RX_LENGTH]; //数据接收数组
bool Flag_Serial_RXcplt = false; //接收完成标志位
uint16_t Serial_RxLength = 0; //接收数据长度

void Serial_Init(void)
{
    // 清空接收缓冲区
    memset(Serial_RxData, 0, MAX_RX_LENGTH);
    Serial_RxLength = 0;
    Flag_Serial_RXcplt = false;

    // 配置接收DMA
    DL_DMA_setSrcAddr(DMA, DMA_CH_RX_CHAN_ID, (uint32_t)(&UART0_INST->RXDATA));
    DL_DMA_setDestAddr(DMA, DMA_CH_RX_CHAN_ID, (uint32_t)(&Serial_RxData[0]));
    DL_DMA_setTransferSize(DMA, DMA_CH_RX_CHAN_ID, MAX_RX_LENGTH);
    DL_DMA_enableChannel(DMA, DMA_CH_RX_CHAN_ID);

    // 配置发送DMA
    DL_DMA_setDestAddr(DMA, DMA_CH_TX_CHAN_ID, (uint32_t)(&UART0_INST->TXDATA));
    DL_DMA_disableChannel(DMA, DMA_CH_TX_CHAN_ID);

    // 输出初始化信息
    MyPrintf("UART0 Serial Init Complete - Ready to receive data\r\n");
}
/**
 * @brief 阻塞式发送数据
 * 
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return uint16_t 格式化后字符长短
 */
uint16_t MyPrintf(char *format, ...)
{
    DL_DMA_disableChannel(DMA, DMA_CH_TX_CHAN_ID);
    char txbuffer[MAX_TX_LENGTH];
    uint16_t len = 0;
    va_list args;
    va_start(args, format);
    len = vsnprintf(txbuffer, MAX_TX_LENGTH, format, args);
    va_end(args);
    for (uint16_t i = 0; i < len; i++)
    {
        DL_UART_transmitDataBlocking(UART0_INST, txbuffer[i]);
    }
    return len;
}

/**
 * @brief DMA发送数据
 * 
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return uint16_t 格式化后字符长短
 */
uint16_t MyPrintf_DMA(char *format, ...)
{
    char Serial_TxData[MAX_TX_LENGTH];
    uint16_t len = 0;
    va_list args;
    va_start(args, format);
    len = vsnprintf(Serial_TxData, MAX_TX_LENGTH, format, args);
    va_end(args);

    DL_DMA_disableChannel(DMA, DMA_CH_TX_CHAN_ID);
    DL_DMA_setSrcAddr(DMA, DMA_CH_TX_CHAN_ID, (uint32_t)(&Serial_TxData[0])); //配置接收DMA的源地址
    DL_DMA_setTransferSize(DMA, DMA_CH_TX_CHAN_ID, len); //配置DMA数据传输大小
    DL_DMA_enableChannel(DMA, DMA_CH_TX_CHAN_ID);

    return len;
}

/* HWT101适配层支持 */
/* 全局UART1句柄定义 */
UART_HandleTypeDef huart1 = {
    .Instance = (void*)UART_1_INST,
    .Init = 0
};

/**
 * @brief UART1发送函数(适配HAL库接口)
 *
 * @param huart UART句柄(忽略，固定使用UART1)
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return uint16_t 发送字节数
 */
uint16_t my_printf(UART_HandleTypeDef* huart, const char* format, ...)
{
    char txbuffer[MAX_TX_LENGTH];
    uint16_t len = 0;
    va_list args;
    va_start(args, format);
    len = vsnprintf(txbuffer, MAX_TX_LENGTH, format, args);
    va_end(args);

    for (uint16_t i = 0; i < len; i++)
    {
        DL_UART_transmitDataBlocking(UART_1_INST, txbuffer[i]);
    }
    return len;
}

/**
 * @brief 处理UART0接收到的数据
 */
void Serial_ProcessRxData(void)
{
    if (Flag_Serial_RXcplt == true)
    {
        Flag_Serial_RXcplt = false;

        // 显示接收到的数据长度
        MyPrintf("UART0 Received %d bytes: ", Serial_RxLength);

        // 显示接收到的数据内容(以十六进制和ASCII格式)
        for (uint16_t i = 0; i < Serial_RxLength && i < MAX_RX_LENGTH; i++)
        {
            MyPrintf("%02X ", Serial_RxData[i]);
        }
        MyPrintf(" | ");

        // 显示ASCII字符(可打印字符)
        for (uint16_t i = 0; i < Serial_RxLength && i < MAX_RX_LENGTH; i++)
        {
            if (Serial_RxData[i] >= 32 && Serial_RxData[i] <= 126)
                MyPrintf("%c", Serial_RxData[i]);
            else
                MyPrintf(".");
        }
        MyPrintf("\r\n");

        // 清空接收缓冲区
        memset(Serial_RxData, 0, MAX_RX_LENGTH);
        Serial_RxLength = 0;
    }
}