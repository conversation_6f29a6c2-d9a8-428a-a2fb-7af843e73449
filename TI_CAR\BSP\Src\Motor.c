#include "Motor.h"

// 宏定义：控制电机转向的GPIO操作
#define SET_FOWARD(X)   DL_GPIO_setPins(DIRC_CTRL_PORT, (X))    // 设置GPIO引脚为高电平，电机正转
#define SET_BACKWARD(X) DL_GPIO_clearPins(DIRC_CTRL_PORT, (X))  // 设置GPIO引脚为低电平，电机反转

// ========== 四个电机对象定义 ==========
// 每个电机都包含：方向、编码器地址、控制引脚、PWM定时器、PWM通道

// 左前轮电机配置
MOTOR_Def_t Motor_Font_Left = {
    .Motor_Dirc = DIRC_FOWARD,                    // 初始方向：正转
    .Motor_Encoder_Addr = &Data_MotorEncoder[0],  // 编码器数据地址：用于读取转速反馈
    .Motor_Turn_Pin = DIRC_CTRL_FONT_LEFT_PIN,    // 方向控制引脚：控制正转/反转
    .Motor_PWM_TIMX = MotorFront_INST,            // PWM定时器：前轮定时器
    .Motor_PWM_CH = DL_TIMER_CC_0_INDEX           // PWM通道：通道0
};

// 右前轮电机配置
MOTOR_Def_t Motor_Font_Right = {
    .Motor_Dirc = DIRC_FOWARD,                    // 初始方向：正转
    .Motor_Encoder_Addr = &Data_MotorEncoder[1],  // 编码器数据地址：数组第1个元素
    .Motor_Turn_Pin = DIRC_CTRL_FONT_RIGHT_PIN,   // 方向控制引脚：右前轮专用引脚
    .Motor_PWM_TIMX = MotorFront_INST,            // PWM定时器：前轮定时器（与左前轮共用）
    .Motor_PWM_CH = DL_TIMER_CC_1_INDEX           // PWM通道：通道1
};

// 左后轮电机配置
MOTOR_Def_t Motor_Back_Left = {
    .Motor_Dirc = DIRC_FOWARD,                    // 初始方向：正转
    .Motor_Encoder_Addr = &Data_MotorEncoder[2],  // 编码器数据地址：数组第2个元素
    .Motor_Turn_Pin = DIRC_CTRL_BACK_LEFT_PIN,    // 方向控制引脚：左后轮专用引脚
    .Motor_PWM_TIMX = MotorBack_INST,             // PWM定时器：后轮定时器
    .Motor_PWM_CH = DL_TIMER_CC_0_INDEX           // PWM通道：通道0
};

// 右后轮电机配置
MOTOR_Def_t Motor_Back_Right = {
    .Motor_Dirc = DIRC_FOWARD,                    // 初始方向：正转
    .Motor_Encoder_Addr = &Data_MotorEncoder[3],  // 编码器数据地址：数组第3个元素
    .Motor_Turn_Pin = DIRC_CTRL_BACK_RIGHT_PIN,   // 方向控制引脚：右后轮专用引脚
    .Motor_PWM_TIMX = MotorBack_INST,             // PWM定时器：后轮定时器（与左后轮共用）
    .Motor_PWM_CH = DL_TIMER_CC_1_INDEX           // PWM通道：通道1
};
/**
 * @brief 电机系统启动函数 - 相当于汽车的"点火启动"
 * 功能：启动PWM定时器，初始化所有电机为停止状态，设置PID控制参数
 */
void Motor_Start(void)
{
    // ========== 第一步：启动PWM定时器（相当于启动发动机） ==========
    DL_TimerG_startCounter(MotorFront_INST);  // 启动前轮PWM定时器，开始产生PWM信号
    DL_TimerG_startCounter(MotorBack_INST);   // 启动后轮PWM定时器，开始产生PWM信号

    // ========== 第二步：设置所有电机初始状态为停止 ==========
    // 就像刚启动的汽车，发动机在转但车子还没动，占空比0%表示电机不转
    Motor_SetDuty(&Motor_Font_Left, 0.0f);   // 左前轮占空比设为0%，电机停止
    Motor_SetDuty(&Motor_Back_Left, 0.0f);   // 左后轮占空比设为0%，电机停止
    Motor_SetDuty(&Motor_Back_Right, 0.0f);  // 右后轮占空比设为0%，电机停止
    Motor_SetDuty(&Motor_Font_Right, 0.0f);  // 右前轮占空比设为0%，电机停止

    // ========== 第三步：初始化PID控制器（相当于校准巡航控制系统） ==========
    // PID控制器用于精确控制电机转速，就像汽车的巡航控制系统
    PID_IQ_Init(&Motor_Font_Left.Motor_PID_Instance);   // 初始化左前轮PID控制器
    PID_IQ_Init(&Motor_Back_Left.Motor_PID_Instance);   // 初始化左后轮PID控制器
    PID_IQ_Init(&Motor_Back_Right.Motor_PID_Instance);  // 初始化右后轮PID控制器
    PID_IQ_Init(&Motor_Font_Right.Motor_PID_Instance);  // 初始化右前轮PID控制器

    // ========== 第四步：设置PID控制参数 ==========
    // PID参数含义：P(比例)=2.0 反应速度，I(积分)=0.5 消除稳态误差，D(微分)=0.1 减少震荡
    // 就像调节汽车巡航控制的灵敏度和稳定性
    PID_IQ_SetParams(&Motor_Font_Left.Motor_PID_Instance, 2.0f, 0.5f, 0.1f);   // 左前轮PID参数
    PID_IQ_SetParams(&Motor_Back_Left.Motor_PID_Instance, 2.0f, 0.5f, 0.1f);   // 左后轮PID参数
    PID_IQ_SetParams(&Motor_Back_Right.Motor_PID_Instance, 2.0f, 0.5f, 0.1f);  // 右后轮PID参数
    PID_IQ_SetParams(&Motor_Font_Right.Motor_PID_Instance, 2.0f, 0.5f, 0.1f);  // 右前轮PID参数
}

/**
 * @brief 设置电机转动方向 - 相当于汽车的"前进/倒车档"
 *
 * @param Motor 要控制的电机对象指针
 * @param Dirc 要设置的方向（DIRC_FOWARD=正转/前进，DIRC_BACKWARD=反转/后退）
 * @return true=设置成功，false=设置失败
 */
static bool Motor_SetDirc(MOTOR_Def_t *Motor, Motor_DIRC_Def_t Dirc)
{
    // 安全检查：确保传入的电机对象指针不为空
    if (Motor == NULL) return false;

    // 根据要求的方向设置对应的GPIO引脚状态
    if (Dirc == DIRC_FOWARD)  // 如果要求正转（前进）
    {
        SET_FOWARD(Motor->Motor_Turn_Pin);  // 设置方向控制引脚为高电平，电机正转
        Motor->Motor_Dirc = DIRC_FOWARD;    // 更新电机对象中的方向记录
        return true;  // 返回设置成功
    }
    else if (Dirc == DIRC_BACKWARD)  // 如果要求反转（后退）
    {
        SET_BACKWARD(Motor->Motor_Turn_Pin);  // 设置方向控制引脚为低电平，电机反转
        Motor->Motor_Dirc = DIRC_BACKWARD;    // 更新电机对象中的方向记录
        return true;  // 返回设置成功
    }

    // 如果传入的方向参数无效，返回失败
    return false;
}

/**
 * @brief 设置电机转速和方向 - 相当于汽车的"油门踏板+档位"
 *
 * @param Motor 要控制的电机对象指针
 * @param value PWM占空比值 (-100 ~ +100)
 *              正值：正转(前进)，数值越大转速越快
 *              负值：反转(后退)，数值越小转速越快
 *              0：停止
 * @return true=设置成功，false=设置失败
 */
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value)
{
    // 安全检查：确保传入的电机对象指针不为空
    if (Motor == NULL) return false;

    // ========== 第一步：限制输入值范围，防止超出安全范围 ==========
    // 就像汽车油门踏板有物理限制，不能踩过头
    if (value > 100.0f) value = 100.0f;    // 最大正转速度限制为100%
    if (value < -100.0f) value = -100.0f;  // 最大反转速度限制为100%

    // ========== 第二步：根据数值正负号自动设置电机转动方向 ==========
    if (value >= 0)  // 如果是正数或零
    {
        Motor_SetDirc(Motor, DIRC_FOWARD);   // 设置为正转方向（前进）
    }
    else  // 如果是负数
    {
        Motor_SetDirc(Motor, DIRC_BACKWARD); // 设置为反转方向（后退）
    }

    // ========== 第三步：设置PWM占空比控制转速 ==========
    // 使用绝对值，因为方向已经通过GPIO控制了，PWM只控制转速大小
    uint32_t duty = (uint32_t)fabs(value);  // 取绝对值并转换为无符号整数

    // 将占空比值写入定时器的比较寄存器，控制PWM输出
    // 就像控制汽车发动机的供油量，占空比越大，电机转得越快
    DL_TimerG_setCaptureCompareValue(Motor->Motor_PWM_TIMX, duty, Motor->Motor_PWM_CH);

    return true;  // 返回设置成功
}

/**
 * @brief 获取电机实际转速 - 相当于汽车的"速度表读数"
 * 功能：读取编码器数据，计算实际转速，并更新到PID控制器中用于闭环控制
 *
 * @param Motor 要读取速度的电机对象指针
 * @param time 两次读取之间的时间间隔(毫秒)，用于计算速度
 * @return true=读取成功，false=读取失败
 */
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time)
{
    // 安全检查：确保传入的电机对象指针不为空
    if (Motor == NULL) return false;

    // ========== 第一步：准备计算所需的数据 ==========
    _iq Interval_Time = _IQ(time / 1000.0f);              // 将毫秒转换为秒（IQ格式定点数）
    _iq Encoder_Value = _IQ(*Motor->Motor_Encoder_Addr);   // 读取编码器累计脉冲数
    *Motor->Motor_Encoder_Addr = 0;                        // 读取后立即清零，为下次计算做准备

    // ========== 第二步：计算瞬时速度 ==========
    // 速度 = 脉冲数 ÷ 时间间隔，单位：脉冲/秒 或 转/秒
    // 就像汽车速度表：距离 ÷ 时间 = 速度
    _iq Speed = _IQdiv(Encoder_Value, Interval_Time);

    // ========== 第三步：根据电机转动方向修正速度符号 ==========
    // 编码器只能检测转动快慢，不能检测方向，需要结合方向信息修正
    if (Motor->Motor_Dirc == DIRC_BACKWARD && Speed > 0)
    {
        Speed = -Speed;  // 如果电机在反转但速度为正，修正为负值
    }
    else if (Motor->Motor_Dirc == DIRC_FOWARD && Speed < 0)
    {
        Speed = -Speed;  // 如果电机在正转但速度为负，修正为正值
    }

    // ========== 第四步：将实际速度更新到PID控制器 ==========
    // PID控制器需要知道当前实际速度，才能与目标速度比较并调整输出
    Motor->Motor_PID_Instance.Acutal_Now = Speed;

    return true;  // 返回读取成功
}