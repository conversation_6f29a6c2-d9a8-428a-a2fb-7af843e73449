# TI智能小车 - 电机控制与循迹模块说明文档

## 📋 文档概述

本文档详细介绍了TI智能小车项目中的两个核心模块：
- **Motor.c** - 电机控制模块
- **Tracker.c** - 循迹传感器模块

这两个模块是智能小车实现自动循迹功能的关键组件。

---

## 🚗 Motor.c - 电机控制模块

### 模块功能概述
电机控制模块负责管理智能小车的四个轮子（左前、右前、左后、右后），实现精确的速度控制和方向控制。

### 核心数据结构

#### 电机对象定义 (MOTOR_Def_t)
每个电机都有以下属性：
- **PWM定时器** - 控制电机转速的PWM信号
- **PWM通道** - 具体的PWM输出通道
- **方向控制引脚** - 控制电机正转/反转的GPIO引脚
- **编码器地址** - 读取电机转速反馈的编码器数据
- **当前方向** - 记录电机是正转还是反转
- **PID控制器** - 实现精确速度控制的PID算法实例

#### 四个电机实例
```
🔧 Motor_Font_Left  - 左前轮
🔧 Motor_Font_Right - 右前轮  
🔧 Motor_Back_Left  - 左后轮
🔧 Motor_Back_Right - 右后轮
```

### 主要功能函数

#### 1. Motor_Start() - 电机系统初始化
**功能说明：**
- 启动前轮和后轮的PWM定时器
- 将所有电机的初始占空比设为0（停止状态）
- 初始化四个电机的PID控制器
- 设置PID参数（P=2.0, I=0.5, D=0.1）

**通俗理解：** 就像启动汽车发动机，让所有系统准备就绪，但车子还没开始动。

#### 2. Motor_SetDuty() - 设置电机转速和方向
**输入参数：**
- `Motor` - 要控制的电机对象
- `value` - 转速值（-100到+100）
  - 正值：正转（前进方向）
  - 负值：反转（后退方向）
  - 数值大小：转速快慢（0=停止，100=最快）

**工作原理：**
1. 检查输入值是否在有效范围内（-100到+100）
2. 根据正负号设置电机转动方向
3. 使用绝对值设置PWM占空比控制转速

**通俗理解：** 就像汽车的油门踏板，踩得越深跑得越快，还能控制前进或倒车。

#### 3. Motor_GetSpeed() - 获取电机实际转速
**功能说明：**
- 读取编码器数据计算实际转速
- 根据时间间隔计算速度（转/秒）
- 考虑电机方向修正速度符号
- 将结果更新到PID控制器中

**通俗理解：** 就像汽车仪表盘的速度表，实时显示当前车速。

### 方向控制机制
```
SET_FOWARD(引脚)  - 设置正转（前进）
SET_BACKWARD(引脚) - 设置反转（后退）
```

---

## 🛤️ Tracker.c - 循迹传感器模块

### 模块功能概述
循迹传感器模块使用8个红外传感器检测地面黑线，计算小车相对于黑线的位置偏差，为循迹控制提供数据支持。

### 传感器布局
```
传感器编号：  0    1    2    3    4    5    6    7
位置(cm)： -5.25 -3.75 -2.25 -0.75 +0.75 +2.25 +3.75 +5.25
           ←←←← 左侧 ←←  中心  →→ 右侧 →→→→
```

### 核心常量定义
- `DIS_INRERVAL = 1.5cm` - 相邻传感器间距
- `TRACK_ON = 1` - 检测到黑线
- `TRACK_OFF = 0` - 未检测到黑线

### 主要功能函数

#### Tracker_Read() - 读取传感器并计算偏差
**输入参数：**
- `tck_ptr` - 8个传感器数据的数组指针
- `offset_ptr` - 计算出的位置偏差值指针

**工作流程：**

1. **读取8个传感器状态**
   ```
   传感器1 → GPIO读取 → 0或1
   传感器2 → GPIO读取 → 0或1
   ...
   传感器8 → GPIO读取 → 0或1
   ```

2. **加权平均计算位置偏差**
   - 遍历所有传感器
   - 对检测到黑线的传感器进行位置加权
   - 计算平均位置偏差

**计算公式：**
```
传感器位置 = (传感器编号 - 3.5) × 1.5cm
位置偏差 = 所有激活传感器位置的加权平均值
```

**返回值含义：**
- `负值` - 小车偏向左侧，需要向右调整
- `正值` - 小车偏向右侧，需要向左调整  
- `0` - 小车在黑线中央，无需调整

**通俗理解：** 就像人走路时眼睛看路，判断自己是否走偏了，偏了多少，该往哪个方向调整。

### 应用场景示例

#### 场景1：小车在黑线中央
```
传感器状态: [0,0,0,1,1,0,0,0]
计算结果: 偏差 ≈ 0cm
控制策略: 直行
```

#### 场景2：小车偏向右侧
```
传感器状态: [0,0,1,1,0,0,0,0]  
计算结果: 偏差 ≈ -1.5cm (负值)
控制策略: 向右转向修正
```

#### 场景3：小车偏向左侧
```
传感器状态: [0,0,0,0,1,1,0,0]
计算结果: 偏差 ≈ +1.5cm (正值)  
控制策略: 向左转向修正
```

---

## 🔄 两模块协同工作原理

### 循迹控制流程
```
1. Tracker.c 读取传感器 → 计算位置偏差
2. 控制算法处理偏差 → 生成转向指令  
3. Motor.c 执行指令 → 调整各轮转速
4. 小车修正方向 → 回到循迹状态
5. 循环执行上述过程
```

### 典型控制策略
- **直行**：四轮同速前进
- **左转**：左轮减速，右轮加速
- **右转**：右轮减速，左轮加速
- **急转**：一侧轮子反转，另一侧正转

---

## 📊 技术特点总结

### Motor.c 特点
✅ **四轮独立控制** - 每个轮子都可以独立设置转速和方向  
✅ **PID闭环控制** - 通过编码器反馈实现精确速度控制  
✅ **方向自动处理** - 根据占空比正负号自动设置转向  
✅ **安全限制** - 占空比限制在±100范围内防止过载  

### Tracker.c 特点  
✅ **8路传感器** - 提供高精度的位置检测  
✅ **加权平均算法** - 准确计算位置偏差  
✅ **实时响应** - 快速检测并反馈位置信息  
✅ **鲁棒性设计** - 处理无线检测等异常情况  

---

## 🎯 应用价值

这两个模块的完美配合，使得TI智能小车能够：
- 🚗 **自动循迹** - 沿着黑线精确行驶
- 🎯 **精确控制** - 实现平滑的转向和速度调节  
- 🔄 **实时响应** - 快速适应路径变化
- 🛡️ **稳定可靠** - 在各种环境下保持良好性能

这是一个典型的**感知-决策-执行**闭环控制系统的优秀实现！
