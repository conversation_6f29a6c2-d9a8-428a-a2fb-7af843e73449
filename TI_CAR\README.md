## Example Summary

Empty project using DriverLib.
This example shows a basic empty project using DriverLib with just main file
and SysConfig initialization.

## Peripherals & Pin Assignments

| Peripheral | Pin | Function |
| --- | --- | --- |
| SYSCTL |  |  |
| DEBUGSS | PA20 | Debug Clock |
| DEBUGSS | PA19 | Debug Data In Out |

## BoosterPacks, Board Resources & Jumper Settings

Visit [LP_MSPM0G3507](https://www.ti.com/tool/LP-MSPM0G3507) for LaunchPad information, including user guide and hardware files.

| Pin | Peripheral | Function | LaunchPad Pin | LaunchPad Settings |
| --- | --- | --- | --- | --- |
| PA20 | DEBUGSS | SWCLK | N/A | <ul><li>PA20 is used by SWD during debugging<br><ul><li>`J101 15:16 ON` Connect to XDS-110 SWCLK while debugging<br><li>`J101 15:16 OFF` Disconnect from XDS-110 SWCLK if using pin in application</ul></ul> |
| PA19 | DEBUGSS | SWDIO | N/A | <ul><li>PA19 is used by SWD during debugging<br><ul><li>`J101 13:14 ON` Connect to XDS-110 SWDIO while debugging<br><li>`J101 13:14 OFF` Disconnect from XDS-110 SWDIO if using pin in application</ul></ul> |

### Device Migration Recommendations
This project was developed for a superset device included in the LP_MSPM0G3507 LaunchPad. Please
visit the [CCS User's Guide](https://software-dl.ti.com/msp430/esd/MSPM0-SDK/latest/docs/english/tools/ccs_ide_guide/doc_guide/doc_guide-srcs/ccs_ide_guide.html#sysconfig-project-migration)
for information about migrating to other MSPM0 devices.

### Low-Power Recommendations
TI recommends to terminate unused pins by setting the corresponding functions to
GPIO and configure the pins to output low or input with internal
pullup/pulldown resistor.

SysConfig allows developers to easily configure unused pins by selecting **Board**→**Configure Unused Pins**.

For more information about jumper configuration to achieve low-power using the
MSPM0 LaunchPad, please visit the [LP-MSPM0G3507 User's Guide](https://www.ti.com/lit/slau873).

## 新增功能说明

### UART0数据接收功能 (已修复)
- **功能**: 增加了完整的UART0数据接收和显示功能
- **特性**:
  - 支持DMA接收、接收超时中断和普通RX中断(多重保障)
  - 自动显示接收数据的十六进制和ASCII格式
  - 接收完成后自动重新配置DMA继续接收
  - LED指示：红灯闪烁=DMA中断，蓝灯闪烁=超时/RX中断
- **使用方法**:
  - 通过UART0(PA10-TX, PA11-RX, 115200波特率)发送数据
  - 系统会自动显示接收到的数据内容和长度
  - 数据格式: "UART0 Received X bytes: [HEX] | [ASCII]"
  - 发送回车(\r)或换行(\n)结束一帧数据

### 相关文件修改
- `BSP/Inc/Serial.h`: 添加接收相关变量声明和函数声明
- `BSP/Src/Serial.c`: 实现数据处理函数Serial_ProcessRxData()，改进DMA初始化
- `APP/Src/Interrupt.c`: 修正中断函数名，完善UART0中断处理，添加多种接收方式
- `APP/Src/Task_App.c`: 在空闲任务中调用数据处理函数，添加测试信息输出

### 故障排除与验证 (全面检查完成)
- **问题**: 中断函数名不匹配 → **解决**: 修正为UART0_IRQHandler ✅
- **问题**: 中断未正确初始化 → **解决**: 避免重复配置，使用系统自动配置 ✅
- **问题**: DMA配置错误 → **解决**: 修正DMA地址配置，使用UART0_INST ✅
- **问题**: LED_RED_TOGGLE宏定义错误 → **解决**: 修正为LED_RED_PIN ✅
- **问题**: 未定义的中断常量 → **解决**: 使用数值0替代DL_UART_MAIN_IIDX_NO_INT ✅
- **问题**: UART1中断函数名错误 → **解决**: 修正为UART1_IRQHandler ✅
- **问题**: 格式化字符串错误 → **解决**: 修正"%.2f,%.2f,%,2f"为"%.2f,%.2f,%.2f" ✅
- **备用方案**: 如果DMA接收失败，自动使用普通RX中断接收 ✅

### 系统配置验证
- **UART0硬件**: PA10(TX), PA11(RX), 115200-8-N-1 ✅
- **中断配置**: DMA_DONE_RX, RX_TIMEOUT_ERROR, RX, EOT_DONE ✅
- **DMA配置**: 通道1(RX), 通道0(TX), 512字节缓冲区 ✅
- **其他功能**: 电机控制、循迹、MPU6050、OLED显示均未受影响 ✅

## Example Usage

Compile, load and run the example.
