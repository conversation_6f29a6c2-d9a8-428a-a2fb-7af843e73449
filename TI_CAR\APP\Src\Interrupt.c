/**
 * @file Interrupt.c
 * <AUTHOR> name (<EMAIL>)
 * @brief 存储的是各种中断相关的
 * @version 0.1
 * @date 2025-07-12
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include "Interrupt.h"

uint8_t enable_group1_irq = 0;
uint32_t ExISR_Flag; //中断判断标志位
bool Flag_MPU6050_Ready = false; //MPU6050是否准备好

#define ISR_IS_GPIO(X)        (ExISR_Flag & (X))
#define GET_RDR_B_VAL(X)      DL_GPIO_readPins(SPD_READER_B_PORT, (X))
#define CLR_GPIOA_ISR_FLAG(X) DL_GPIO_clearInterruptStatus(GPIOA, (X))

/**
 * @brief Systick时钟中断
 * 
 */
void SysTick_Handler(void)
{
    SysTick_Increasment();
}

/**
 * @brief 串口0中断
 *
 */
void UART0_IRQHandler(void)
{
    uint32_t interruptStatus = DL_UART_Main_getPendingInterrupt(UART0_INST);

    //DMA接收完成中断
    if (interruptStatus == DL_UART_MAIN_IIDX_DMA_DONE_RX)
    {
        // 计算实际接收的数据长度
        Serial_RxLength = MAX_RX_LENGTH - DL_DMA_getTransferSize(DMA, DMA_CH_RX_CHAN_ID);

        // 立即显示调试信息
        LED_RED_TOGGLE(); // 红灯闪烁表示收到DMA中断

        // 设置接收完成标志
        Flag_Serial_RXcplt = true;

        // 重新配置DMA接收
        DL_DMA_disableChannel(DMA, DMA_CH_RX_CHAN_ID);
        DL_DMA_setTransferSize(DMA, DMA_CH_RX_CHAN_ID, MAX_RX_LENGTH);
        DL_DMA_enableChannel(DMA, DMA_CH_RX_CHAN_ID);

        DL_UART_clearInterruptStatus(UART0_INST, DL_UART_MAIN_IIDX_DMA_DONE_RX);
    }

    //接收超时中断处理
    else if (interruptStatus == DL_UART_MAIN_IIDX_RX_TIMEOUT_ERROR)
    {
        // 计算实际接收的数据长度
        Serial_RxLength = MAX_RX_LENGTH - DL_DMA_getTransferSize(DMA, DMA_CH_RX_CHAN_ID);

        if (Serial_RxLength > 0)
        {
            // 蓝灯闪烁表示收到超时中断
            LED_BLUE_TOGGLE();

            // 设置接收完成标志
            Flag_Serial_RXcplt = true;

            // 重新配置DMA接收
            DL_DMA_disableChannel(DMA, DMA_CH_RX_CHAN_ID);
            DL_DMA_setTransferSize(DMA, DMA_CH_RX_CHAN_ID, MAX_RX_LENGTH);
            DL_DMA_enableChannel(DMA, DMA_CH_RX_CHAN_ID);
        }

        DL_UART_clearInterruptStatus(UART0_INST, DL_UART_MAIN_IIDX_RX_TIMEOUT_ERROR);
    }

    //普通接收中断处理(备用方案)
    else if (interruptStatus == DL_UART_MAIN_IIDX_RX)
    {
        static uint16_t rx_index = 0;

        // 直接读取接收到的字节
        uint8_t rxData = DL_UART_receiveData(UART0_INST);

        if (rx_index < MAX_RX_LENGTH - 1)
        {
            Serial_RxData[rx_index] = rxData;
            rx_index++;

            // 如果收到回车或换行，认为一帧数据结束
            if (rxData == '\r' || rxData == '\n')
            {
                Serial_RxLength = rx_index;
                Flag_Serial_RXcplt = true;
                rx_index = 0;
                LED_BLUE_TOGGLE(); // 蓝灯表示收到完整数据
            }
        }
        else
        {
            // 缓冲区满，重置
            rx_index = 0;
        }

        DL_UART_clearInterruptStatus(UART0_INST, DL_UART_MAIN_IIDX_RX);
    }

    //发送完成中断
    else if (interruptStatus == DL_UART_MAIN_IIDX_EOT_DONE)
    {
        DL_DMA_disableChannel(DMA, DMA_CH_TX_CHAN_ID);
        LED_BOARD_TOGGLE();
        DL_UART_clearInterruptStatus(UART0_INST, DL_UART_MAIN_IIDX_EOT_DONE);
    }

    // 处理其他可能的中断
    else if (interruptStatus != DL_UART_MAIN_IIDX_NO_INT)
    {
        // 有其他中断，清除所有状态
        DL_UART_clearInterruptStatus(UART0_INST, 0xFFFFFFFF);
    }
}

/* HWT101相关变量 */
uint8_t HWT101_RxBuffer[64]; //HWT101接收缓冲区
bool Flag_HWT101_DataReady = false; //HWT101数据就绪标志

/**
 * @brief 串口1中断(HWT101数据接收)
 *
 */
void UART_1_INST_IRQHandler(void)
{
    //接收中断处理
    if (DL_UART_Main_getPendingInterrupt(UART_1_INST) == DL_UART_MAIN_IIDX_RX)
    {
        uint8_t rxData = DL_UART_receiveData(UART_1_INST);
        static uint8_t rxIndex = 0;

        HWT101_RxBuffer[rxIndex] = rxData;
        rxIndex++;

        if (rxIndex >= sizeof(HWT101_RxBuffer))
        {
            rxIndex = 0;
        }

        Flag_HWT101_DataReady = true;
        DL_UART_clearInterruptStatus(UART_1_INST, DL_UART_MAIN_IIDX_RX);
    }
}

/**
 * @brief 外部中断
 *
 */
void GROUP1_IRQHandler(void)
{
    //判断是不是由GPIOA触发的中断
    if (DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1) == GPIO_MULTIPLE_GPIOA_INT_IIDX)
    {
        //查看哪个IO进入外部中断
        ExISR_Flag = DL_GPIO_getEnabledInterruptStatus(GPIOA,
                                                       SPD_READER_A_FONT_LEFT_A_PIN | SPD_READER_A_FONT_RIGHT_A_PIN |
                                                           SPD_READER_A_BACK_LEFT_A_PIN |
                                                           SPD_READER_A_BACK_RIGHT_A_PIN | GPIO_MPU6050_PIN_INT_PIN);

        if (ISR_IS_GPIO(SPD_READER_A_FONT_LEFT_A_PIN)) //左前轮
        {
            if (GET_RDR_B_VAL(SPD_READER_B_FONT_LEFT_B_PIN)) (*Motor_Font_Left.Motor_Encoder_Addr)++;
            else (*Motor_Font_Left.Motor_Encoder_Addr)--;
            CLR_GPIOA_ISR_FLAG(SPD_READER_A_FONT_LEFT_A_PIN);
        }

        if (ISR_IS_GPIO(SPD_READER_A_FONT_RIGHT_A_PIN)) //右前轮
        {
            if (GET_RDR_B_VAL(SPD_READER_B_FONT_RIGHT_B_PIN)) (*Motor_Font_Left.Motor_Encoder_Addr)++;
            else (*Motor_Font_Left.Motor_Encoder_Addr)--;
            CLR_GPIOA_ISR_FLAG(SPD_READER_A_FONT_RIGHT_A_PIN);
        }

        if (ISR_IS_GPIO(SPD_READER_A_BACK_LEFT_A_PIN)) //左后轮
        {
            if (GET_RDR_B_VAL(SPD_READER_B_BACK_LEFT_B_PIN)) (*Motor_Back_Left.Motor_Encoder_Addr)++;
            else (*Motor_Back_Left.Motor_Encoder_Addr)++;
            CLR_GPIOA_ISR_FLAG(SPD_READER_A_BACK_LEFT_A_PIN);
        }

        if (ISR_IS_GPIO(SPD_READER_A_BACK_RIGHT_A_PIN)) //右后轮
        {
            if (GET_RDR_B_VAL(SPD_READER_B_BACK_RIGHT_B_PIN)) (*Motor_Back_Right.Motor_Encoder_Addr)++;
            else (*Motor_Back_Right.Motor_Encoder_Addr)--;
            CLR_GPIOA_ISR_FLAG(SPD_READER_A_BACK_RIGHT_A_PIN);
        }

        //MPU6050读取数据
        if (ISR_IS_GPIO(GPIO_MPU6050_PIN_INT_PIN))
        {
            if (Flag_MPU6050_Ready == false) Flag_MPU6050_Ready = true;
            CLR_GPIOA_ISR_FLAG(GPIO_MPU6050_PIN_INT_PIN);
        }
    }
}

/**
 * @brief 中断初始化
 * 
 */
void Interrupt_Init(void)
{
    NVIC_EnableIRQ(1);

    //UART0中断已在SYSCFG_DL_UART0_init()中配置，这里只需启用NVIC和清除状态
    NVIC_EnableIRQ(UART0_INST_INT_IRQN);
    DL_UART_clearInterruptStatus(UART0_INST, DL_UART_MAIN_IIDX_DMA_DONE_RX);
    DL_UART_clearInterruptStatus(UART0_INST, DL_UART_MAIN_IIDX_EOT_DONE);
    DL_UART_clearInterruptStatus(UART0_INST, DL_UART_MAIN_IIDX_RX_TIMEOUT_ERROR);
    DL_UART_clearInterruptStatus(UART0_INST, DL_UART_MAIN_IIDX_RX);

    //额外启用RX中断作为备用方案
    DL_UART_enableInterrupt(UART0_INST, DL_UART_INTERRUPT_RX);

    //初始化UART1中断(HWT101)
    DL_UART_enableInterrupt(UART_1_INST, DL_UART_INTERRUPT_RX);
    NVIC_EnableIRQ(UART_1_INST_INT_IRQN);
    DL_UART_clearInterruptStatus(UART_1_INST, DL_UART_MAIN_IIDX_RX);

    CLR_GPIOA_ISR_FLAG(SPD_READER_A_FONT_LEFT_A_PIN);
    CLR_GPIOA_ISR_FLAG(SPD_READER_A_FONT_RIGHT_A_PIN);
    CLR_GPIOA_ISR_FLAG(SPD_READER_A_BACK_LEFT_A_PIN);
    CLR_GPIOA_ISR_FLAG(SPD_READER_A_BACK_RIGHT_A_PIN);
    CLR_GPIOA_ISR_FLAG(GPIO_MPU6050_PIN_INT_PIN);
}
